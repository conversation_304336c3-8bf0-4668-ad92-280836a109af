package chagine.medicalinstitute.pojo;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;


@TableName(value ="medical_history")
@Data
public class MedicalHistory {
    /**
     * ID
     */
    @TableId
    private Long id;

//    /**
//     * 开始时间
//     */
//    private String startTime;
//
//    /**
//     * 结束时间
//     */
//    private String endTime;

    /**
     * 开始年份
     */
    private String startYear;

    /**
     * 结束年份
     */
    private String endYear;

    /**
     * 结束年份
     */
    private String titleName;

    /**
     * 事件描述
     */
    private String eventDesc;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createUser;

    /**
     * 变更时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime chanTime;

    /**
     * 变更人
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long chanUser;

    /**
     * 变更引起的请求
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long chanRequest;

    /**
     * 租户id(合作机构租户号)
     */
    @TableField(fill = FieldFill.INSERT)
    private Long tenaId;

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        MedicalHistory other = (MedicalHistory) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
//            && (this.getStartTime() == null ? other.getStartTime() == null : this.getStartTime().equals(other.getStartTime()))
//            && (this.getEndTime() == null ? other.getEndTime() == null : this.getEndTime().equals(other.getEndTime()))
            && (this.getStartYear() == null ? other.getStartYear() == null : this.getStartYear().equals(other.getStartYear()))
            && (this.getEndYear() == null ? other.getEndYear() == null : this.getEndYear().equals(other.getEndYear()))
            && (this.getTitleName() == null ? other.getTitleName() == null : this.getTitleName().equals(other.getTitleName()))
            && (this.getEventDesc() == null ? other.getEventDesc() == null : this.getEventDesc().equals(other.getEventDesc()))
            && (this.getCreateTime() == null ? other.getCreateTime() == null : this.getCreateTime().equals(other.getCreateTime()))
            && (this.getCreateUser() == null ? other.getCreateUser() == null : this.getCreateUser().equals(other.getCreateUser()))
            && (this.getChanTime() == null ? other.getChanTime() == null : this.getChanTime().equals(other.getChanTime()))
            && (this.getChanUser() == null ? other.getChanUser() == null : this.getChanUser().equals(other.getChanUser()))
            && (this.getChanRequest() == null ? other.getChanRequest() == null : this.getChanRequest().equals(other.getChanRequest()))
            && (this.getTenaId() == null ? other.getTenaId() == null : this.getTenaId().equals(other.getTenaId()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
//        result = prime * result + ((getStartTime() == null) ? 0 : getStartTime().hashCode());
//        result = prime * result + ((getEndTime() == null) ? 0 : getEndTime().hashCode());
        result = prime * result + ((getStartYear() == null) ? 0 : getStartYear().hashCode());
        result = prime * result + ((getEndYear() == null) ? 0 : getEndYear().hashCode());
        result = prime * result + ((getTitleName() == null) ? 0 : getTitleName().hashCode());
        result = prime * result + ((getEventDesc() == null) ? 0 : getEventDesc().hashCode());
        result = prime * result + ((getCreateTime() == null) ? 0 : getCreateTime().hashCode());
        result = prime * result + ((getCreateUser() == null) ? 0 : getCreateUser().hashCode());
        result = prime * result + ((getChanTime() == null) ? 0 : getChanTime().hashCode());
        result = prime * result + ((getChanUser() == null) ? 0 : getChanUser().hashCode());
        result = prime * result + ((getChanRequest() == null) ? 0 : getChanRequest().hashCode());
        result = prime * result + ((getTenaId() == null) ? 0 : getTenaId().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
//        sb.append(", startTime=").append(startTime);
//        sb.append(", endTime=").append(endTime);
        sb.append(", startYear=").append(startYear);
        sb.append(", endYear=").append(endYear);
        sb.append(", titleName=").append(titleName);
        sb.append(", eventDesc=").append(eventDesc);
        sb.append(", createTime=").append(createTime);
        sb.append(", createUser=").append(createUser);
        sb.append(", chanTime=").append(chanTime);
        sb.append(", chanUser=").append(chanUser);
        sb.append(", chanRequest=").append(chanRequest);
        sb.append(", tenaId=").append(tenaId);
        sb.append("]");
        return sb.toString();
    }
}