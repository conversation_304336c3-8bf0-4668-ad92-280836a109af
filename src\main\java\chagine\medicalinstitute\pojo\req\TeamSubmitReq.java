package chagine.medicalinstitute.pojo.req;


import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.Data;

import java.util.List;

@Data
public class TeamSubmitReq {

    /**
     * ID
     */
    @JsonDeserialize(using = chagine.medicalinstitute.serialize.StringJsonDeserializer.class)
    @JsonSerialize(using = chagine.medicalinstitute.serialize.LongJsonSerializer.class)
    private Long teamId;

    /**
     * 团队名称
     */
    private String teamName;

    /**
     * 团队照片id
     */
    @JsonDeserialize(using = chagine.medicalinstitute.serialize.StringJsonDeserializer.class)
    @JsonSerialize(using = chagine.medicalinstitute.serialize.LongJsonSerializer.class)
    private Long teamPhotoResourceId;
//
//    /**
//     * 团队照片路径
//     */
//    private String teamPhotoResourcePath;

    /**
     * 团队介绍
     */
    private String teamIntroduce;


    /**
     * 团队成员
     */
    private List<TeamUserReq> teamUserList;
}
