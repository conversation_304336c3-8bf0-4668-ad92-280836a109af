package chagine.medicalinstitute.util;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

public class UtilTime {
    /**
     * 获取当前时间的字符串格式（年-月-日 时:分）
     * @return 当前时间的字符串格式
     */
    public static String getCurrentFormattedTimeMinute() {
        // 获取当前日期和时间
        LocalDateTime now = LocalDateTime.now();
        // 定义日期时间格式
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm");
        // 格式化为字符串
        return now.format(formatter);
    }

}
