package chagine.medicalinstitute.feign;

import chagine.medicalinstitute.feign.vo.UserVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@FeignClient("basic")
public interface BasicClient {


    @RequestMapping(value = "/user/get_all_user_list", method = RequestMethod.GET)
    List<UserVo> getAlllUserList(@RequestParam(value = "key", required = false) String key, @RequestHeader(name = "tenantid") Long tenantId);


    @RequestMapping(value = "/user/get_all_user_list_by_tenant", method = RequestMethod.GET)
    List<UserVo> getAllUserListByTenant(@RequestParam(value = "key", required = false) String key, @RequestHeader(name = "tenantid") Long tenantId);


}
