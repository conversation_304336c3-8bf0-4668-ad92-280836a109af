package chagine.medicalinstitute.pojo;


import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

@TableName(value ="medical_research_trend")
@Data
public class MedicalResearchTrend {

    /**
     * ID
     */
    @TableId
    private Long id;

    /**
     * 动向标题
     */
    private String trendTitle;

    /**
     * 作者单位
     */
    private String authorUnit;

    /**
     * 研究动向照片id
     */
    private Long trendPhotoResourceId;


    /**
     * 研究动向照片路径
     */
    private String trendPhotoResourcePath;

    /**
     * 内容
     */
    private String content;

    /**
     * 发布时间
     */
    private String releaseTime;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createUser;

    /**
     * 变更时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime chanTime;

    /**
     * 变更人
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long chanUser;

    /**
     * 变更引起的请求
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long chanRequest;

    /**
     * 租户id(合作机构租户号)
     */
    @TableField(fill = FieldFill.INSERT)
    private Long tenaId;

}
