package chagine.medicalinstitute.pojo;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

@TableName(value ="knowledge_outstand_person")
@Data
public class KnowledgeOutstandPerson {
    /**
     * ID
     */
    @TableId
    private Long id;

    /**
     * 人物姓名
     */
    private String personName;

    /**
     * 时期
     */
    private String period;

    /**
     * 籍贯
     */
    private String nativePlace;

    /**
     * 生平事迹
     */
    private String lifeAndEvents;

    /**
     * 学术经验
     */
    private String academicExperience;

    /**
     * 治病方法
     */
    private String curingDiseaseMethod;

    /**
     * 杰出人物照片id
     */
    private Long personPhotoResourceId;


    /**
     * 杰出人物动向照片路径
     */
    private String personPhotoResourcePath;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createUser;

    /**
     * 变更时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime chanTime;

    /**
     * 变更人
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long chanUser;

    /**
     * 变更引起的请求
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long chanRequest;

    /**
     * 租户id(合作机构租户号)
     */
    @TableField(fill = FieldFill.INSERT)
    private Long tenaId;
}
