package chagine.medicalinstitute.feign;

import chagine.medicalinstitute.feign.vo.FileVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

@FeignClient("resource")
public interface ResourceClient {

    /**
     * 服务间文件上传
     * @param file     待上传的文件
     */
    @PostMapping(value = "/feign/file/upload_file",
            consumes = MediaType.MULTIPART_FORM_DATA_VALUE,
            produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    FileVo uploadFile(@RequestPart(value = "file") MultipartFile file,
                      @RequestHeader(value = "tenantid") Long tenantId,
                      @RequestHeader(value = "userid") Long userId
    );

    @RequestMapping(value = "/file/get_file", method = RequestMethod.GET)
    FileVo getFile(@RequestParam(value = "file_id", required = false) Long fileId, @RequestHeader(name = "tenantid") Long tenantId);

}