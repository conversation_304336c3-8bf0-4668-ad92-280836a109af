spring:
  main:
    allow-bean-definition-overriding: true
    allow-circular-references: true
  application:
    name: medical-institute
  cloud:
    nacos:
      discovery:
        #        server-addr: 192.168.1.10:8848
        server-addr: 125.124.28.118:8848
        namespace: f9f8c966-cc50-44f3-a1e3-e773f68a974f
        group: SERVICE
        username: app
        password: app
      config:
        #        server-addr: 192.168.1.10:8848
        server-addr: 125.124.28.118:8848
        namespace: f9f8c966-cc50-44f3-a1e3-e773f68a974f
        group: SERVICE
        username: app
        password: app
        file-extension: yaml
        #共享配置
        shared-configs:
          - dataId: dev.${spring.cloud.nacos.config.file-extension}
            group: SHARE
            refresh: false
          - dataId: database.${spring.cloud.nacos.config.file-extension}
            group: SHARE
            refresh: false
          - dataId: redis.${spring.cloud.nacos.config.file-extension}
            group: SHARE
            refresh: false
          - dataId: mybatis.${spring.cloud.nacos.config.file-extension}
            group: SHARE
            refresh: false
          - dataId: log.${spring.cloud.nacos.config.file-extension}
            group: SHARE
            refresh: false
  profiles:
    active: dev, wzswry

#logging:
#  level:
#    adapter: DEBUG
#    chagine:
#      nurse:
#        examination: DEBUG

#mybatis-plus:
#  configuration:
#    logImpl: org.apache.ibatis.logging.stdout.StdOutImpl
#server:
#  port: 8080