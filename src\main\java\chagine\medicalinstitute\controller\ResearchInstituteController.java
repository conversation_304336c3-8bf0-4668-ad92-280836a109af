package chagine.medicalinstitute.controller;


import chagine.medicalinstitute.feign.BasicClient;
import chagine.medicalinstitute.feign.vo.UserVo;
import chagine.medicalinstitute.pojo.req.KeywordReq;
import chagine.medicalinstitute.pojo.req.ResearchSearchReq;
import chagine.medicalinstitute.pojo.req.TeamSubmitReq;
import chagine.medicalinstitute.pojo.req.TeamUserReq;
import chagine.medicalinstitute.pojo.vo.MedicalBriefVo;
import chagine.medicalinstitute.pojo.vo.MedicalHistoryVo;
import chagine.medicalinstitute.pojo.vo.MedicalResearchTrendVo;
import chagine.medicalinstitute.pojo.vo.MedicalTeamVo;
import chagine.medicalinstitute.service.ResearchInstituteService;
import chagine.service.api.core.utils.Assert;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

@RequestMapping("/research_institute")
@RestController
@Slf4j
public class ResearchInstituteController {

    @Resource
    private ResearchInstituteService researchInstituteService;

    @Resource
    private BasicClient basicClient;


    /**
     * 测试 stirng
     */
    @GetMapping("/test_string")
    public String testString() {
        return "hello world";
    }



    /**
     * 新增/编辑 简介  0
     */
    @PostMapping("/add_or_edit_briefxxxx")
    public void addOrEditBriefxxxx(@RequestBody MedicalBriefVo req) {
        Assert.error(req.getBrief() == null, "brief不能为空");
        researchInstituteService.addOrEditBriefxxxx(req);
    }

    /**
     * 新增/编辑 简介
     */
    @PostMapping("/add_or_edit_brief")
    public void addOrEditBrief(@RequestBody MedicalBriefVo req) {
        Assert.error(req.getBrief() == null, "brief不能为空");
        researchInstituteService.addOrEditBrief(req);
    }

    /**
     * 获取简介
     */
    @PostMapping("/get_brief")
    public MedicalBriefVo getBrief() {
        return researchInstituteService.getBrief();
    }

    /**
     * 获取团队列表 0
     */
    @PostMapping("/get_teamsxxxxxx")
    public List<MedicalTeamVo> getTeamsxxxxxxxxx() {
        return researchInstituteService.getTeamList();
    }

    /**
     * 搜索团队 - 根据团队名称/成员搜索
     */
    @PostMapping("/get_teams")
    public List<MedicalTeamVo> searchTeams(@RequestBody KeywordReq req) {
        log.info("搜索团队 - 根据团队名称/成员搜索, keyword: {}", req.getKeyword());
        return researchInstituteService.searchTeams(req.getKeyword());
    }

    /**
     * 获取团队详情
     */
    @PostMapping("/get_team")
    public MedicalTeamVo getTeam(@RequestBody MedicalTeamVo req) {
        Assert.error(req.getTeamId() == null, "团队ID不能为空");
        return researchInstituteService.getTeam(req.getTeamId());
    }


//    /**
//     * 新增/编辑团队 - 信息
//     */
//    @PostMapping("/add_or_edit_team")
//    public void addOrEditTeam(@RequestBody TeamSubmitReq req) {
//        Assert.error(req.getTeamName() == null, "团队名称不能为空");
//        researchInstituteService.addOrEditTeam(req);
//    }

    /**
     * 查询basic-user表
     */
    @PostMapping("/query_basic_user_table")
    private List<TeamUserReq> queryBasicUserTable() {
//        researchInstituteService.queryBasicUserTable();
        List<UserVo> alllUserList = basicClient.getAllUserListByTenant(null, 100100L);

        // 将UserVo转换为TeamUserReq
        return alllUserList.stream().map(userVo -> {
            TeamUserReq teamUserReq = new TeamUserReq();
            teamUserReq.setUserId(userVo.getUserId());
            teamUserReq.setUserName(userVo.getUserName());
            teamUserReq.setDepaId(userVo.getDepaId() != null ? userVo.getDepaId().toString() : null);
            teamUserReq.setDepaName(userVo.getDepaName());
            teamUserReq.setTitle(userVo.getTitle());
            return teamUserReq;
        }).collect(Collectors.toList());
    }

    /**
     * 新增/编辑团队 - 信息
     */
    @PostMapping("/add_or_edit_team")
    public void addOrEditTeam(@RequestBody TeamSubmitReq req) {
        Assert.error(req.getTeamName() == null, "团队名称不能为空");

        log.info("======================resourceid=========================="+req.getTeamPhotoResourceId());

        // 处理前端可能传递的 "undefined" 字符串
        if (req.getTeamPhotoResourceId() != null && req.getTeamPhotoResourceId().equals(0L)) {
            req.setTeamPhotoResourceId(null);
        }
        
        researchInstituteService.addOrEditTeam(req);
    }

    /**
     * 处理团队 - 图片 0
     */
    @PostMapping("/upload_team_photo")
    public String uploadTeamPhoto(
            @RequestParam("file") MultipartFile file,
            @RequestParam("team_id") String teamId,
            @RequestParam(value = "file_id", required = false) String fileId
    ) {

        Assert.error(file == null || file.isEmpty(), "上传文件不能为空");
        Assert.error(teamId == null, "团队ID不能为空");

        // 先将String teamId转换为Long
        Long teamIdLong = null;
        Long fileIdLong = null;
        try {
            teamIdLong = Long.parseLong(teamId);
            if (fileId != null) {
                fileIdLong = Long.parseLong(fileId);
            }
        } catch (NumberFormatException e) {
            return "团队ID格式不正确";
        }
        return researchInstituteService.uploadTeamPhoto(file, teamIdLong, fileIdLong);
    }


    /**
     * 删除团队
     */
    @PostMapping("/delete_team")
    public void deleteTeam(@RequestBody MedicalTeamVo req) {
        Assert.error(req.getTeamId() == null, "团队ID不能为空");
        researchInstituteService.deleteTeam(req.getTeamId());
    }

    /**
     * 获取医院历史列表 - 根据开始年份升序排序
     */
    @PostMapping("/get_historie_list")
    public List<MedicalHistoryVo> getHistorieList() {
        return researchInstituteService.getHistories();
    }

    /**
     * 获取医院历史列表（带排序）  --0
     */
    @PostMapping("/get_historie_list_sorted")
    public List<MedicalHistoryVo> getHistorieListSorted(@RequestParam(value = "sort_by", required = false) String sortBy,
                                                    @RequestParam(value = "sort_order", required = false, defaultValue = "asc") String sortOrder) {
        return researchInstituteService.getHistoriesSorted(sortBy, sortOrder);
    }

//    /**
//     * 分页获取医院历史列表
//     */
//    @PostMapping("/get_histories_page")
//    public Map<String, Object> getHistoriesPage(@RequestParam(value = "page_num", required = false, defaultValue = "1") Integer pageNum,
//                                               @RequestParam(value = "page_size", required = false, defaultValue = "10") Integer pageSize,
//                                               @RequestParam(value = "sort_by", required = false) String sortBy,
//                                               @RequestParam(value = "sort_order", required = false, defaultValue = "asc") String sortOrder) {
//        return researchInstituteService.getHistoriesPage(pageNum, pageSize, sortBy, sortOrder);
//    }

    /**
     * 获取医院历史详情
     */
    @PostMapping("/get_history")
    public MedicalHistoryVo getHistory(@RequestBody MedicalHistoryVo req) {
        Assert.error(req.getHistoryId() == null, "历史事件ID不能为空");
        return researchInstituteService.getHistory(req.getHistoryId());
    }

    /**
     * 新增/编辑医院历史
     */
    @PostMapping("/add_or_edit_history")
    public void addOrEditHistory(@RequestBody MedicalHistoryVo history) {
        Assert.error(history.getTitleName() == null, "事件标题不能为空");
        researchInstituteService.addOrEditHistory(history);
    }

    /**
     * 批量新增医院历史 --0
     */
    @PostMapping("/batch_add_historie_list")
    public void batchAddHistorieList(@RequestBody List<MedicalHistoryVo> histories) {
        Assert.error(histories == null || histories.isEmpty(), "历史事件列表不能为空");
        researchInstituteService.batchAddHistories(histories);
    }

    /**
     * 删除医院历史
     */
    @PostMapping("/delete_history")
    public void deleteHistory(@RequestBody MedicalHistoryVo req) {
        Assert.error(req.getHistoryId() == null, "历史事件ID不能为空");
        researchInstituteService.deleteHistory(req.getHistoryId());
    }

    /**
     * 批量删除医院历史 --0
     */
    @PostMapping("/batch_delete_historie_list")
    public void batchDeleteHistorieList(@RequestBody List<Long> historyIds) {
        Assert.error(historyIds == null || historyIds.isEmpty(), "历史事件ID列表不能为空");
        researchInstituteService.batchDeleteHistories(historyIds);
    }


    /**
     * 研究动向 - 获取列表（动态标题，作者单位，发布时间）
     */
    @PostMapping("/get_research_trend_list")
    public List<MedicalResearchTrendVo> getResearchTrendList() {
        return researchInstituteService.getResearchList();
    }

    /**
     * 研究动向 - 编辑/新增研究动向（研究动向id，动态标题，作者单位，封面照片，动态内容）
     */
    @PostMapping("/add_or_edit_research")
    public void addOrEditResearch(@RequestBody MedicalResearchTrendVo req) {
        Assert.error(req.getTrendTitle() == null, "动态标题不能为空");
        Assert.error(req.getAuthorUnit() == null, "作者单位不能为空");

        log.info("======================research coverPhotoResourceId=========================="+req.getTrendPhotoResourceId());

        // 处理前端可能传递的 0 值
        if (req.getTrendPhotoResourceId() != null && req.getTrendPhotoResourceId().equals(0L)) {
            req.setTrendPhotoResourceId(null);
        }

        researchInstituteService.addOrEditResearch(req);
    }

    /**
     * 研究动向 - 获取详情
     */
    @PostMapping("/get_research")
    public MedicalResearchTrendVo getResearch(@RequestBody MedicalResearchTrendVo req) {
        Assert.error(req.getTrendId() == null, "研究动向ID不能为空");
        return researchInstituteService.getResearch(req.getTrendId());
    }

    /**
     * 研究动向 - 删除
     */
    @PostMapping("/delete_research")
    public void deleteResearch(@RequestBody MedicalResearchTrendVo req) {
        Assert.error(req.getTrendId() == null, "研究动向ID不能为空");
        researchInstituteService.deleteResearch(req.getTrendId());
    }

    /**
     * 研究动向 - 搜索（根据标题或作者单位搜索）
     */
    @PostMapping("/search_research")
    public List<MedicalResearchTrendVo> searchResearch(@RequestBody KeywordReq req) {
        log.info("搜索研究动向 - 根据标题或作者单位搜索, keyword: {}", req.getKeyword());
        return researchInstituteService.searchResearch(req.getKeyword());
    }

    /**
     * 研究动向 - 高级搜索（关键词 + 日期范围筛选）
     */
    @PostMapping("/search_research_advanced")
    public List<MedicalResearchTrendVo> searchResearchAdvanced(@RequestBody ResearchSearchReq req) {
        log.info("搜索研究动向 - 高级搜索, keyword: {}, startDate: {}, endDate: {}",
                req.getKeyword(), req.getStartDate(), req.getEndDate());
        return researchInstituteService.searchResearch(req);
    }
}

