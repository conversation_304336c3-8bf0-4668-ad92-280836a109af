package chagine.medicalinstitute.pojo;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;


@TableName(value ="medical_team")
@Data
public class MedicalTeam {
    /**
     * ID
     */
    @TableId
    private Long id;

    /**
     * 团队名称
     */
    private String teamName;

    /**
     * 团队照片id
     */
    private Long teamPhotoResourceId;

    /**
     * 团队照片路径
     */
    private String teamPhotoResourcePath;

    /**
     * 团队介绍
     */
    private String teamIntroduce;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createUser;

    /**
     * 变更时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime chanTime;

    /**
     * 变更人
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long chanUser;

    /**
     * 变更引起的请求
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long chanRequest;

    /**
     * 租户id(合作机构租户号)
     */
    @TableField(fill = FieldFill.INSERT)
    private Long tenaId;

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        MedicalTeam other = (MedicalTeam) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getTeamName() == null ? other.getTeamName() == null : this.getTeamName().equals(other.getTeamName()))
            && (this.getTeamPhotoResourceId() == null ? other.getTeamPhotoResourceId() == null : this.getTeamPhotoResourceId().equals(other.getTeamPhotoResourceId()))
            && (this.getTeamPhotoResourcePath() == null ? other.getTeamPhotoResourcePath() == null : this.getTeamPhotoResourcePath().equals(other.getTeamPhotoResourcePath()))
            && (this.getTeamIntroduce() == null ? other.getTeamIntroduce() == null : this.getTeamIntroduce().equals(other.getTeamIntroduce()))
            && (this.getCreateTime() == null ? other.getCreateTime() == null : this.getCreateTime().equals(other.getCreateTime()))
            && (this.getCreateUser() == null ? other.getCreateUser() == null : this.getCreateUser().equals(other.getCreateUser()))
            && (this.getChanTime() == null ? other.getChanTime() == null : this.getChanTime().equals(other.getChanTime()))
            && (this.getChanUser() == null ? other.getChanUser() == null : this.getChanUser().equals(other.getChanUser()))
            && (this.getChanRequest() == null ? other.getChanRequest() == null : this.getChanRequest().equals(other.getChanRequest()))
            && (this.getTenaId() == null ? other.getTenaId() == null : this.getTenaId().equals(other.getTenaId()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getTeamName() == null) ? 0 : getTeamName().hashCode());
        result = prime * result + ((getTeamPhotoResourceId() == null) ? 0 : getTeamPhotoResourceId().hashCode());
        result = prime * result + ((getTeamPhotoResourcePath() == null) ? 0 : getTeamPhotoResourcePath().hashCode());
        result = prime * result + ((getTeamIntroduce() == null) ? 0 : getTeamIntroduce().hashCode());
        result = prime * result + ((getCreateTime() == null) ? 0 : getCreateTime().hashCode());
        result = prime * result + ((getCreateUser() == null) ? 0 : getCreateUser().hashCode());
        result = prime * result + ((getChanTime() == null) ? 0 : getChanTime().hashCode());
        result = prime * result + ((getChanUser() == null) ? 0 : getChanUser().hashCode());
        result = prime * result + ((getChanRequest() == null) ? 0 : getChanRequest().hashCode());
        result = prime * result + ((getTenaId() == null) ? 0 : getTenaId().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", teamName=").append(teamName);
        sb.append(", teamPhotoResourceId=").append(teamPhotoResourceId);
        sb.append(", teamPhotoResourcePath=").append(teamPhotoResourcePath);
        sb.append(", teamIntroduce=").append(teamIntroduce);
        sb.append(", createTime=").append(createTime);
        sb.append(", createUser=").append(createUser);
        sb.append(", chanTime=").append(chanTime);
        sb.append(", chanUser=").append(chanUser);
        sb.append(", chanRequest=").append(chanRequest);
        sb.append(", tenaId=").append(tenaId);
        sb.append("]");
        return sb.toString();
    }
}