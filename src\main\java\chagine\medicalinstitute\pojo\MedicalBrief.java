package chagine.medicalinstitute.pojo;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 
 * @TableName medical_brief
 */
@TableName(value ="medical_brief")
@Data
public class MedicalBrief {
    /**
     * ID
     */
    @TableId
    private Long id;

    /**
     * 简介内容(富文本)
     */
    private String brief;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createUser;

    /**
     * 变更时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime chanTime;

    /**
     * 变更人
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long chanUser;

    /**
     * 变更引起的请求
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long chanRequest;

    /**
     * 租户id(合作机构租户号)
     */
    @TableField(fill = FieldFill.INSERT)
    private Long tenaId;

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        MedicalBrief other = (MedicalBrief) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getBrief() == null ? other.getBrief() == null : this.getBrief().equals(other.getBrief()))
            && (this.getCreateTime() == null ? other.getCreateTime() == null : this.getCreateTime().equals(other.getCreateTime()))
            && (this.getCreateUser() == null ? other.getCreateUser() == null : this.getCreateUser().equals(other.getCreateUser()))
            && (this.getChanTime() == null ? other.getChanTime() == null : this.getChanTime().equals(other.getChanTime()))
            && (this.getChanUser() == null ? other.getChanUser() == null : this.getChanUser().equals(other.getChanUser()))
            && (this.getChanRequest() == null ? other.getChanRequest() == null : this.getChanRequest().equals(other.getChanRequest()))
            && (this.getTenaId() == null ? other.getTenaId() == null : this.getTenaId().equals(other.getTenaId()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getBrief() == null) ? 0 : getBrief().hashCode());
        result = prime * result + ((getCreateTime() == null) ? 0 : getCreateTime().hashCode());
        result = prime * result + ((getCreateUser() == null) ? 0 : getCreateUser().hashCode());
        result = prime * result + ((getChanTime() == null) ? 0 : getChanTime().hashCode());
        result = prime * result + ((getChanUser() == null) ? 0 : getChanUser().hashCode());
        result = prime * result + ((getChanRequest() == null) ? 0 : getChanRequest().hashCode());
        result = prime * result + ((getTenaId() == null) ? 0 : getTenaId().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", brief=").append(brief);
        sb.append(", createTime=").append(createTime);
        sb.append(", createUser=").append(createUser);
        sb.append(", chanTime=").append(chanTime);
        sb.append(", chanUser=").append(chanUser);
        sb.append(", chanRequest=").append(chanRequest);
        sb.append(", tenaId=").append(tenaId);
        sb.append("]");
        return sb.toString();
    }
}