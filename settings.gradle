//插件管理
pluginManagement {
    repositories {
        maven { url = "${rootProject.projectDir.absolutePath}/localRepository" }
        mavenLocal()
        maven {
            allowInsecureProtocol = true
            url 'http://183.134.88.76:8081/repository/maven-public/'
            credentials {
                username = "chagine_application"
                password = "chagine_application"
            }
        }
        mavenCentral()
        gradlePluginPortal()
        google()
    }
}

//依赖管理
dependencyResolutionManagement {
    repositoriesMode.set(RepositoriesMode.FAIL_ON_PROJECT_REPOS)

    //仓库
    repositories {
        maven { url = "${rootProject.projectDir.absolutePath}/localRepository" }
        mavenLocal()
        maven {
            allowInsecureProtocol = true
            url 'http://183.134.88.76:8081/repository/maven-public/'
            credentials {
                username = "chagine_application"
                password = "chagine_application"
            }
        }
        maven {
            allowInsecureProtocol = true
            url 'https://maven.aliyun.com/repository/public/'
        }
        maven {
            allowInsecureProtocol = true
            url 'https://maven.aliyun.com/repository/google/'
        }
        maven {
            allowInsecureProtocol = true
            url 'https://maven.aliyun.com/repository/jcenter/'
        }
        maven {
            allowInsecureProtocol = true
            url 'https://maven.aliyun.com/nexus/content/groups/public/'
        }
        mavenCentral()
    }
}

rootProject.name = 'chagine-medical-institute'