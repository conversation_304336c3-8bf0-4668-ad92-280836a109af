package chagine.medicalinstitute.service;

import chagine.medicalinstitute.feign.ResourceClient;
import chagine.medicalinstitute.feign.vo.FileVo;
import chagine.medicalinstitute.pojo.KnowledgeOutstandPerson;
import chagine.medicalinstitute.pojo.KnowledgePersonRelationBookmaking;
import chagine.medicalinstitute.pojo.vo.KnowledgeOutstandPersonVo;
import chagine.medicalinstitute.service.sqlService.KnowledgeOutstandPersonService;
import chagine.medicalinstitute.service.sqlService.KnowledgePersonRelationBookmakingService;
import chagine.service.api.basic.client.UserClient;
import chagine.service.api.core.enums.RequestAttribute;
import chagine.service.api.core.utils.Assert;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@Slf4j
public class KnowledgeManagementService {

    @Resource
    private KnowledgePersonRelationBookmakingService knowledgePersonRelationBookmakingService;

    @Resource
    private KnowledgeOutstandPersonService knowledgeOutstandPersonService;

    @Resource
    private ResourceClient resourceClient;

    @Autowired
    private UserClient userClient;

    /**
     * 获取杰出人物列表
     */
    public List<KnowledgeOutstandPersonVo> getOutstandPersonList() {
        List<KnowledgeOutstandPerson> entityList = knowledgeOutstandPersonService.list();

        List<KnowledgeOutstandPersonVo> voList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(entityList)) {
            // 获取所有人物的著作关系
            List<Long> personIds = entityList.stream()
                    .map(KnowledgeOutstandPerson::getId)
                    .collect(Collectors.toList());

            Map<Long, List<KnowledgePersonRelationBookmaking>> personBookmakingMap = getPersonBookmakingMap(personIds);

            for (KnowledgeOutstandPerson entity : entityList) {
                KnowledgeOutstandPersonVo vo = convertToPersonVo(entity, personBookmakingMap.get(entity.getId()));
                voList.add(vo);
            }
        }

        return voList;
    }

    /**
     * 新增/编辑杰出人物
     */
    @Transactional(rollbackFor = Exception.class)
    public void addOrEditOutstandPerson(KnowledgeOutstandPersonVo req) {
        // 参数校验
        Assert.error(req.getPersonName() == null, "人物姓名不能为空");
        Assert.error(req.getPeriod() == null, "时期不能为空");

        KnowledgeOutstandPerson entity;
        // 判断是新增还是编辑
        if (req.getPersonId() != null) {
            // 编辑
            entity = knowledgeOutstandPersonService.getById(req.getPersonId());
            Assert.error(entity == null, "未找到对应的人物信息");
        } else {
            // 新增
            entity = new KnowledgeOutstandPerson();
        }


        // 处理封面照片资源ID
        Long PhotoResourceId = req.getPersonPhotoResourceId();
        String trendPhotoResourcePath = null;

        log.info("========执行到coverPhotoResourcePath处理=============coverPhotoResourceId: {}", PhotoResourceId);

        if (PhotoResourceId != null) {
            FileVo file = resourceClient.getFile(PhotoResourceId, RequestAttribute.TENANT_ID.getLong());
            Assert.error(file == null, "封面照片不存在");
            trendPhotoResourcePath = file.getPath();
        }

        // VO转Entity
        entity.setPersonName(req.getPersonName());
        entity.setPeriod(req.getPeriod());
        entity.setNativePlace(req.getNativePlace());
        entity.setLifeAndEvents(req.getLifeAndEvents());
        entity.setAcademicExperience(req.getAcademicExperience());
        entity.setCuringDiseaseMethod(req.getCuringDiseaseMethod());
        entity.setPersonPhotoResourceId(PhotoResourceId);
        entity.setPersonPhotoResourcePath(trendPhotoResourcePath);

        knowledgeOutstandPersonService.saveOrUpdate(entity);

        // 确保人物ID有效
        Long finalPersonId = entity.getId();
        Assert.error(finalPersonId == null, "人物保存失败，无法获取人物ID");

        // 处理著作关系
        updatePersonBookmakingRelations(finalPersonId, req.getBookmakingList());
    }

    /**
     * 获取杰出人物详情
     */
    public KnowledgeOutstandPersonVo getOutstandPerson(Long personId) {
        Assert.error(personId == null, "人物ID不能为空");
        KnowledgeOutstandPerson person = knowledgeOutstandPersonService.getById(personId);
        Assert.error(person == null, "未找到对应的人物信息");

        // 获取著作关系
        List<KnowledgePersonRelationBookmaking> bookmakingList = getPersonBookmakingList(personId);

        return convertToPersonVo(person, bookmakingList);
    }

    /**
     * 删除杰出人物
     */
    @Transactional(rollbackFor = Exception.class)
    public void deleteOutstandPerson(Long personId) {
        Assert.error(personId == null, "人物ID不能为空");
        KnowledgeOutstandPerson person = knowledgeOutstandPersonService.getById(personId);
        Assert.error(person == null, "未找到对应的人物信息");

        // 删除著作关系
        LambdaQueryWrapper<KnowledgePersonRelationBookmaking> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(KnowledgePersonRelationBookmaking::getPersonId, personId);
        knowledgePersonRelationBookmakingService.remove(wrapper);

        // 删除人物记录
        knowledgeOutstandPersonService.removeById(personId);
    }

    /**
     * 搜索杰出人物 - 根据姓名或时期搜索
     */
    public List<KnowledgeOutstandPersonVo> searchOutstandPerson(String keyword) {
        if (keyword == null || keyword.trim().isEmpty()) {
            // 如果关键词为空，返回所有人物
            return getOutstandPersonList();
        }

        // 根据姓名或时期搜索
        LambdaQueryWrapper<KnowledgeOutstandPerson> wrapper = Wrappers.lambdaQuery();
        wrapper.like(KnowledgeOutstandPerson::getPersonName, keyword.trim())
               .or()
               .like(KnowledgeOutstandPerson::getPeriod, keyword.trim());

        List<KnowledgeOutstandPerson> entityList = knowledgeOutstandPersonService.list(wrapper);

        List<KnowledgeOutstandPersonVo> voList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(entityList)) {
            // 获取所有人物的著作关系
            List<Long> personIds = entityList.stream()
                    .map(KnowledgeOutstandPerson::getId)
                    .collect(Collectors.toList());

            Map<Long, List<KnowledgePersonRelationBookmaking>> personBookmakingMap = getPersonBookmakingMap(personIds);

            for (KnowledgeOutstandPerson entity : entityList) {
                KnowledgeOutstandPersonVo vo = convertToPersonVo(entity, personBookmakingMap.get(entity.getId()));
                voList.add(vo);
            }
        }

        return voList;
    }

    // ----------------------------------  私有辅助方法 ----------------------------------

    /**
     * 获取多个人物的著作关系映射
     */
    private Map<Long, List<KnowledgePersonRelationBookmaking>> getPersonBookmakingMap(List<Long> personIds) {
        if (CollectionUtils.isEmpty(personIds)) {
            return Map.of();
        }

        LambdaQueryWrapper<KnowledgePersonRelationBookmaking> wrapper = Wrappers.lambdaQuery();
        wrapper.in(KnowledgePersonRelationBookmaking::getPersonId, personIds);
        List<KnowledgePersonRelationBookmaking> relations = knowledgePersonRelationBookmakingService.list(wrapper);

        return relations.stream()
                .collect(Collectors.groupingBy(KnowledgePersonRelationBookmaking::getPersonId));
    }

    /**
     * 获取单个人物的著作关系列表
     */
    private List<KnowledgePersonRelationBookmaking> getPersonBookmakingList(Long personId) {
        if (personId == null) {
            return new ArrayList<>();
        }

        LambdaQueryWrapper<KnowledgePersonRelationBookmaking> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(KnowledgePersonRelationBookmaking::getPersonId, personId);
        return knowledgePersonRelationBookmakingService.list(wrapper);
    }

    /**
     * 转换人物实体为VO
     */
    private KnowledgeOutstandPersonVo convertToPersonVo(KnowledgeOutstandPerson entity, List<KnowledgePersonRelationBookmaking> bookmakingList) {
        if (entity == null) {
            return null;
        }

        KnowledgeOutstandPersonVo vo = new KnowledgeOutstandPersonVo();
        vo.setPersonId(entity.getId());
        vo.setPersonName(entity.getPersonName());
        vo.setPeriod(entity.getPeriod());
        vo.setNativePlace(entity.getNativePlace());
        vo.setLifeAndEvents(entity.getLifeAndEvents());
        vo.setAcademicExperience(entity.getAcademicExperience());
        vo.setCuringDiseaseMethod(entity.getCuringDiseaseMethod());
        vo.setPersonPhotoResourceId(entity.getPersonPhotoResourceId());
        vo.setPersonPhotoResourcePath(entity.getPersonPhotoResourcePath());

        // 转换著作列表
        List<KnowledgeOutstandPersonVo.BookmakingInfo> bookmakingInfoList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(bookmakingList)) {
            for (KnowledgePersonRelationBookmaking relation : bookmakingList) {
                KnowledgeOutstandPersonVo.BookmakingInfo info = new KnowledgeOutstandPersonVo.BookmakingInfo();
                info.setBookmakingId(relation.getBookmakingId());
                info.setBookmakingName(relation.getBookmakingName());
                bookmakingInfoList.add(info);
            }
        }
        vo.setBookmakingList(bookmakingInfoList);

        return vo;
    }

    /**
     * 更新人物著作关系
     */
    private void updatePersonBookmakingRelations(Long personId, List<KnowledgeOutstandPersonVo.BookmakingInfo> newBookmakingList) {
        if (personId == null) {
            return;
        }

        // 1. 删除现有关系
        LambdaQueryWrapper<KnowledgePersonRelationBookmaking> deleteWrapper = Wrappers.lambdaQuery();
        deleteWrapper.eq(KnowledgePersonRelationBookmaking::getPersonId, personId);
        knowledgePersonRelationBookmakingService.remove(deleteWrapper);

        // 2. 添加新关系
        if (!CollectionUtils.isEmpty(newBookmakingList)) {
            List<KnowledgePersonRelationBookmaking> toAdd = new ArrayList<>();
            for (KnowledgeOutstandPersonVo.BookmakingInfo bookmaking : newBookmakingList) {
                if (bookmaking.getBookmakingId() != null && bookmaking.getBookmakingName() != null) {
                    KnowledgePersonRelationBookmaking relation = new KnowledgePersonRelationBookmaking();
                    relation.setPersonId(personId);
                    relation.setBookmakingId(bookmaking.getBookmakingId());
                    relation.setBookmakingName(bookmaking.getBookmakingName());
                    // 可以从人物信息中获取姓名
                    KnowledgeOutstandPerson person = knowledgeOutstandPersonService.getById(personId);
                    if (person != null) {
                        relation.setPersonName(person.getPersonName());
                    }
                    toAdd.add(relation);
                }
            }

            if (!toAdd.isEmpty()) {
                knowledgePersonRelationBookmakingService.saveBatch(toAdd);
            }
        }
    }
}
