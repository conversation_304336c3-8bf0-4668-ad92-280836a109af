package chagine.medicalinstitute.config;

import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.reflection.MetaObject;
import org.springframework.stereotype.Component;
import chagine.service.api.core.enums.RequestAttribute;

import java.time.LocalDateTime;

@Slf4j
@Component
public class MyMetaObjectHandler implements MetaObjectHandler {

    @Override
    public void insertFill(MetaObject metaObject) {
        log.info("开始插入填充...");
        
        // 安全获取用户ID和租户ID
        Long userId = getSafeUserId();
        Long tenantId = getSafeTenantId();
        Long requestId = getSafeRequestId();
        
        // 填充创建相关字段
        this.strictInsertFill(metaObject, "createTime", LocalDateTime.class, LocalDateTime.now());
        this.strictInsertFill(metaObject, "createUser", Long.class, userId);
        this.strictInsertFill(metaObject, "tenaId", Long.class, tenantId);
        
        // 填充变更相关字段
        this.strictInsertFill(metaObject, "chanTime", LocalDateTime.class, LocalDateTime.now());
        this.strictInsertFill(metaObject, "chanUser", Long.class, userId);
        this.strictInsertFill(metaObject, "chanRequest", Long.class, requestId);
    }

    @Override
    public void updateFill(MetaObject metaObject) {
        log.info("开始更新填充...");
        
        // 安全获取用户ID和请求ID
        Long userId = getSafeUserId();
        Long requestId = getSafeRequestId();
        
        // 填充变更相关字段
//        this.strictUpdateFill(metaObject, "chanTime", LocalDateTime.class, LocalDateTime.now());
//        this.strictUpdateFill(metaObject, "chanUser", Long.class, userId);
//        this.strictUpdateFill(metaObject, "chanRequest", Long.class, requestId);
        // 方案1：使用 setFieldValByName 强制覆盖
        this.setFieldValByName("chanTime", LocalDateTime.now(), metaObject);
        this.setFieldValByName("chanUser", userId, metaObject);
        this.setFieldValByName("chanRequest", requestId, metaObject);
    }
    
    /**
     * 安全获取用户ID
     */
    private Long getSafeUserId() {
        try {
            Long userId = RequestAttribute.USER_ID.getLong();
            return userId != null ? userId : 1L; // 默认用户ID
        } catch (Exception e) {
            log.warn("获取用户ID失败，使用默认值: {}", e.getMessage());
            return 1L; // 默认用户ID
        }
    }
    
    /**
     * 安全获取租户ID
     */
    private Long getSafeTenantId() {
        try {
            Long tenantId = RequestAttribute.TENANT_ID.getLong();
            return tenantId != null ? tenantId : 100100L; // 默认租户ID
        } catch (Exception e) {
            log.warn("获取租户ID失败，使用默认值: {}", e.getMessage());
            return 100100L; // 默认租户ID
        }
    }
    
    /**
     * 安全获取请求ID
     */
    private Long getSafeRequestId() {
        try {
            // 这里可能需要根据实际情况调整获取请求ID的方式
            return System.currentTimeMillis(); // 使用时间戳作为请求ID
        } catch (Exception e) {
            log.warn("获取请求ID失败，使用默认值: {}", e.getMessage());
            return 1L;
        }
    }
}