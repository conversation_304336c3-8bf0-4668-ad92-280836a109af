package chagine.medicalinstitute.service.sqlService;


import chagine.medicalinstitute.mapper.MedicalRelationTeamUserMapper;
import chagine.medicalinstitute.pojo.MedicalRelationTeamUser;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import java.util.List;


@Service
public class MedicalRelationTeamUserService extends ServiceImpl<MedicalRelationTeamUserMapper, MedicalRelationTeamUser> {

    /**
     * 根据团队ID删除所有关联记录
     * @param teamId 团队ID
     * @return 是否删除成功
     */
    public boolean removeByTeamId(Long teamId) {
        if (teamId == null) {
            return false;
        }
        LambdaQueryWrapper<MedicalRelationTeamUser> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(MedicalRelationTeamUser::getTeamId, teamId);
        return this.remove(wrapper);
    }

    /**
     * 根据团队ID查询所有关联记录
     * @param teamId 团队ID
     * @return 关联记录列表
     */
    public List<MedicalRelationTeamUser> listByTeamId(Long teamId) {
        if (teamId == null) {
            return null;
        }
        LambdaQueryWrapper<MedicalRelationTeamUser> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(MedicalRelationTeamUser::getTeamId, teamId);
        return this.list(wrapper);
    }
}
