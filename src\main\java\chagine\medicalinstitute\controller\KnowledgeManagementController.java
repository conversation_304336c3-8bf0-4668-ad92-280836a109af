package chagine.medicalinstitute.controller;

import chagine.medicalinstitute.pojo.req.KeywordReq;
import chagine.medicalinstitute.pojo.vo.KnowledgeOutstandPersonVo;
import chagine.medicalinstitute.service.KnowledgeManagementService;
import chagine.service.api.core.utils.Assert;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@RequestMapping("/knowledge_management")
@RestController
@Slf4j
public class KnowledgeManagementController {

    @Resource
    private KnowledgeManagementService knowledgeManagementService;


    /**
     * 杰出人物 - 获取列表（人物id，人物姓名，时期，文献著作）
     */
    @PostMapping("/get_outstand_person_list")
    public List<KnowledgeOutstandPersonVo> getOutstandPersonList() {
        return knowledgeManagementService.getOutstandPersonList();
    }

    /**
     * 杰出人物 - 编辑/新增人物信息
     */
    @PostMapping("/add_or_edit_outstand_person")
    public void addOrEditOutstandPerson(@RequestBody KnowledgeOutstandPersonVo req) {
        Assert.error(req.getPersonName() == null, "人物姓名不能为空");
        Assert.error(req.getPeriod() == null, "时期不能为空");

        log.info("======================outstand person=========================="+req.getPersonName());

        knowledgeManagementService.addOrEditOutstandPerson(req);
    }

    /**
     * 杰出人物 - 获取详情
     */
    @PostMapping("/get_outstand_person")
    public KnowledgeOutstandPersonVo getOutstandPerson(@RequestBody KnowledgeOutstandPersonVo req) {
        Assert.error(req.getPersonId() == null, "人物ID不能为空");
        return knowledgeManagementService.getOutstandPerson(req.getPersonId());
    }

    /**
     * 杰出人物 - 删除
     */
    @PostMapping("/delete_outstand_person")
    public void deleteOutstandPerson(@RequestBody KnowledgeOutstandPersonVo req) {
        Assert.error(req.getPersonId() == null, "人物ID不能为空");
        knowledgeManagementService.deleteOutstandPerson(req.getPersonId());
    }

    /**
     * 杰出人物 - 搜索（根据姓名或时期搜索）
     */
    @PostMapping("/search_outstand_person")
    public List<KnowledgeOutstandPersonVo> searchOutstandPerson(@RequestBody KeywordReq req) {
        log.info("搜索杰出人物 - 根据姓名或时期搜索, keyword: {}", req.getKeyword());
        return knowledgeManagementService.searchOutstandPerson(req.getKeyword());
    }
}
