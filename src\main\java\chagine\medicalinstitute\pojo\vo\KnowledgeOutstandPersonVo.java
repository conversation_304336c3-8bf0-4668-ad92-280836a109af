package chagine.medicalinstitute.pojo.vo;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.Data;

import java.util.List;

@Data
public class KnowledgeOutstandPersonVo {
    /**
     * 人物ID
     */
    @JsonDeserialize(using = chagine.medicalinstitute.serialize.StringJsonDeserializer.class)
    @JsonSerialize(using = chagine.medicalinstitute.serialize.LongJsonSerializer.class)
    private Long personId;

    /**
     * 人物姓名
     */
    private String personName;

    /**
     * 时期
     */
    private String period;

    /**
     * 籍贯
     */
    private String nativePlace;

    /**
     * 生平事迹
     */
    private String lifeAndEvents;

    /**
     * 学术经验
     */
    private String academicExperience;

    /**
     * 治病方法
     */
    private String curingDiseaseMethod;

    /**
     * 杰出人物照片id
     */
    @JsonDeserialize(using = chagine.medicalinstitute.serialize.StringJsonDeserializer.class)
    @JsonSerialize(using = chagine.medicalinstitute.serialize.LongJsonSerializer.class)
    private Long personPhotoResourceId;

    /**
     * 杰出人物照片路径
     */
    private String personPhotoResourcePath;

    /**
     * 文献著作列表
     */
    private List<BookmakingInfo> bookmakingList;

    @Data
    public static class BookmakingInfo {
        /**
         * 著作ID
         */
        @JsonDeserialize(using = chagine.medicalinstitute.serialize.StringJsonDeserializer.class)
        @JsonSerialize(using = chagine.medicalinstitute.serialize.LongJsonSerializer.class)
        private Long bookmakingId;

        /**
         * 著作名称
         */
        private String bookmakingName;
    }
}
