package chagine.medicalinstitute.pojo;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;


@TableName(value ="medical_relation_team_user")
@Data
public class MedicalRelationTeamUser {
    /**
     * ID
     */
    @TableId
    private Long id;

    /**
     * 团队id
     */
    private Long teamId;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 用户信息
     */
    private String userInfo;

    /**
     * 用户名字
     */
    private String userName;

    /**
     * 科室id
     */
    private String depaId;

    /**
     * 科室名称
     */
    private String depaName;

    /**
     * 职务
     */
    private String title;


    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createUser;

    /**
     * 变更时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime chanTime;

    /**
     * 变更人
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long chanUser;

    /**
     * 变更引起的请求
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long chanRequest;

    /**
     * 租户id(合作机构租户号)
     */
    @TableField(fill = FieldFill.INSERT)
    private Long tenaId;

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        MedicalRelationTeamUser other = (MedicalRelationTeamUser) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getTeamId() == null ? other.getTeamId() == null : this.getTeamId().equals(other.getTeamId()))
            && (this.getUserId() == null ? other.getUserId() == null : this.getUserId().equals(other.getUserId()))
            && (this.getUserInfo() == null ? other.getUserInfo() == null : this.getUserInfo().equals(other.getUserInfo()))
            && (this.getUserName() == null ? other.getUserName() == null : this.getUserName().equals(other.getUserName()))
            && (this.getTitle() == null ? other.getTitle() == null : this.getTitle().equals(other.getTitle()))
            && (this.getCreateTime() == null ? other.getCreateTime() == null : this.getCreateTime().equals(other.getCreateTime()))
            && (this.getCreateUser() == null ? other.getCreateUser() == null : this.getCreateUser().equals(other.getCreateUser()))
            && (this.getChanTime() == null ? other.getChanTime() == null : this.getChanTime().equals(other.getChanTime()))
            && (this.getChanUser() == null ? other.getChanUser() == null : this.getChanUser().equals(other.getChanUser()))
            && (this.getChanRequest() == null ? other.getChanRequest() == null : this.getChanRequest().equals(other.getChanRequest()))
            && (this.getTenaId() == null ? other.getTenaId() == null : this.getTenaId().equals(other.getTenaId()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getTeamId() == null) ? 0 : getTeamId().hashCode());
        result = prime * result + ((getUserId() == null) ? 0 : getUserId().hashCode());
        result = prime * result + ((getUserInfo() == null) ? 0 : getUserInfo().hashCode());
        result = prime * result + ((getUserName() == null) ? 0 : getUserName().hashCode());
        result = prime * result + ((getTitle() == null) ? 0 : getTitle().hashCode());
        result = prime * result + ((getCreateTime() == null) ? 0 : getCreateTime().hashCode());
        result = prime * result + ((getCreateUser() == null) ? 0 : getCreateUser().hashCode());
        result = prime * result + ((getChanTime() == null) ? 0 : getChanTime().hashCode());
        result = prime * result + ((getChanUser() == null) ? 0 : getChanUser().hashCode());
        result = prime * result + ((getChanRequest() == null) ? 0 : getChanRequest().hashCode());
        result = prime * result + ((getTenaId() == null) ? 0 : getTenaId().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", teamId=").append(teamId);
        sb.append(", userId=").append(userId);
        sb.append(", userInfo=").append(userInfo);
        sb.append(", userName=").append(userName);
        sb.append(", userJob=").append(title);
        sb.append(", createTime=").append(createTime);
        sb.append(", createUser=").append(createUser);
        sb.append(", chanTime=").append(chanTime);
        sb.append(", chanUser=").append(chanUser);
        sb.append(", chanRequest=").append(chanRequest);
        sb.append(", tenaId=").append(tenaId);
        sb.append("]");
        return sb.toString();
    }
}