package chagine.medicalinstitute.serialize;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import org.springframework.util.StringUtils;

import java.io.IOException;

/**
 * Created by lyd on 2017/9/2.
 */
public class StringJsonDeserializer extends JsonDeserializer<Long> {
    @Override
    public Long deserialize(JsonParser p, DeserializationContext ctxt) throws IOException, JsonProcessingException {
        String text = p.getText();
        
        // 处理空值、undefined、null字符串
        if (StringUtils.isEmpty(text) || 
            "undefined".equals(text) || 
            "null".equals(text)) {
            return null;
        }
        
        try {
            return Long.valueOf(text);
        } catch (NumberFormatException e) {
            // 如果转换失败，返回null而不是抛出异常
            return null;
        }
    }
}
