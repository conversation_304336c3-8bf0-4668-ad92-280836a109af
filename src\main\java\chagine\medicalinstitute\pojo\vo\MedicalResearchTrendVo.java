package chagine.medicalinstitute.pojo.vo;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.Data;

@Data
public class MedicalResearchTrendVo {

    /**
     * ID
     */
    @JsonDeserialize(using = chagine.medicalinstitute.serialize.StringJsonDeserializer.class)
    @JsonSerialize(using = chagine.medicalinstitute.serialize.LongJsonSerializer.class)
    private Long trendId;

    /**
     * 动向标题
     */
    private String trendTitle;

    /**
     * 作者单位
     */
    private String authorUnit;

    /**
     * 研究动向照片id
     */
    @JsonDeserialize(using = chagine.medicalinstitute.serialize.StringJsonDeserializer.class)
    @JsonSerialize(using = chagine.medicalinstitute.serialize.LongJsonSerializer.class)
    private Long trendPhotoResourceId;

    /**
     * 研究动向照片路径
     */
    private String trendPhotoResourcePath;

    /**
     * 内容
     */
    private String content;

    /**
     * 发布时间
     */
    private String releaseTime;

}
