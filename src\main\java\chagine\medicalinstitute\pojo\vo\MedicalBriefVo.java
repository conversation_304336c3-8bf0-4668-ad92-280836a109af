package chagine.medicalinstitute.pojo.vo;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.Data;

@Data
public class MedicalBriefVo {

    /**
     * ID
     */
    @JsonDeserialize(using = chagine.medicalinstitute.serialize.StringJsonDeserializer.class)
    @JsonSerialize(using = chagine.medicalinstitute.serialize.LongJsonSerializer.class)
    private Long briefId;

    /**
     * 简介内容(富文本)
     */
    private String brief;

}
