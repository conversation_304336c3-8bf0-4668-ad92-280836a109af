package chagine.medicalinstitute.pojo.vo;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.Data;


@TableName(value ="medical_history")
@Data
public class MedicalHistoryVo {
    @JsonDeserialize(using = chagine.medicalinstitute.serialize.StringJsonDeserializer.class)
    @JsonSerialize(using = chagine.medicalinstitute.serialize.LongJsonSerializer.class)
    private Long historyId;

//    /**
//     * 开始时间
//     */
//    private String startTime;
//
//    /**
//     * 结束时间
//     */
//    private String endTime;

    /**
     * 开始年份
     */
    private String startYear;

    /**
     * 结束年份
     */
    private String endYear;

    /**
     * 结束年份
     */
    private String titleName;

    /**
     * 事件描述
     */
    private String eventDesc;

}