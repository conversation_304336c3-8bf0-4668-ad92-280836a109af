package chagine.medicalinstitute.service;

import chagine.medicalinstitute.feign.ResourceClient;
import chagine.medicalinstitute.feign.vo.FileVo;
import chagine.medicalinstitute.pojo.*;
import chagine.medicalinstitute.pojo.req.ResearchSearchReq;
import chagine.medicalinstitute.pojo.req.TeamSubmitReq;
import chagine.medicalinstitute.pojo.req.TeamUserReq;
import chagine.medicalinstitute.pojo.vo.MedicalBriefVo;
import chagine.medicalinstitute.pojo.vo.MedicalHistoryVo;
import chagine.medicalinstitute.pojo.vo.MedicalResearchTrendVo;
import chagine.medicalinstitute.pojo.vo.MedicalTeamVo;
import chagine.medicalinstitute.service.sqlService.*;
import chagine.medicalinstitute.util.UtilTime;
import chagine.service.api.basic.client.UserClient;
import chagine.service.api.basic.request.UserInfoQuery;
import chagine.service.api.basic.response.UserInfo;
import chagine.service.api.core.enums.RequestAttribute;
import chagine.service.api.core.utils.Assert;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class ResearchInstituteService {

    @Resource
    private MedicalBriefService medicalBriefService;
    
    @Resource
    private MedicalTeamService medicalTeamService;
    
    @Resource
    private MedicalHistoryService medicalHistoryService;

    @Resource
    private MedicalRelationTeamUserService medicalRelationTeamUserService;

    @Resource
    private MedicalResearchTrendService medicalResearchTrendService;

    @Resource
    private ResourceClient resourceClient;

    @Autowired
    private UserClient userClient;

    /**
     * 新增/编辑 简介 0
     */
    @Transactional(rollbackFor = Exception.class)
    public void addOrEditBriefxxxx(MedicalBriefVo req) {
        // 参数校验
        Assert.error(req.getBrief() == null, "简介内容不能为空");
        
        MedicalBrief entity;
        // 判断是新增还是编辑
        if (req.getBriefId() != null) {
            // 编辑
            entity = medicalBriefService.getById(req.getBriefId());
            Assert.error(entity == null, "未找到对应的简介记录");
        } else {
            // 新增
            entity = new MedicalBrief();
        }
        
        entity.setBrief(req.getBrief());
        
        
        medicalBriefService.saveOrUpdate(entity);
        
        // 更新返回的ID
        if (req.getBriefId() == null) {
            req.setBriefId(entity.getId());
        }
    }


    /**
     * 新增/编辑 简介（表中只保留一条记录）
     */
    @Transactional(rollbackFor = Exception.class)
    public void addOrEditBrief(MedicalBriefVo req) {
        // 参数校验
        Assert.error(req.getBrief() == null, "简介内容不能为空");

        // 查询表中是否已有记录
        List<MedicalBrief> existingBriefs = medicalBriefService.list();
        MedicalBrief entity;

        if (!existingBriefs.isEmpty()) {
            // 如果有记录，获取第一条（应该只有一条）
            entity = existingBriefs.get(0);
        } else {
            // 如果没有记录，创建新实体
            entity = new MedicalBrief();
        }

        // 设置简介内容
        entity.setBrief(req.getBrief());

        // 保存或更新
        medicalBriefService.saveOrUpdate(entity);

    }


    /**
     * 获取简介
     */
    public MedicalBriefVo getBrief() {
        List<MedicalBrief> briefList = medicalBriefService.list();
        
        // 处理没有数据的情况
        if (CollectionUtils.isEmpty(briefList)) {
            return new MedicalBriefVo();
        }
        
        // 转换为VO对象
        MedicalBriefVo vo = new MedicalBriefVo();
        MedicalBrief entity = briefList.get(0);
        vo.setBriefId(entity.getId());
        vo.setBrief(entity.getBrief());
        
        return vo;
    }

    /**
     * 获取团队列表
     */
    public List<MedicalTeamVo> getTeamList() {
        List<MedicalTeam> entityList = medicalTeamService.list();
        
        // 转换为VO列表
        List<MedicalTeamVo> voList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(entityList)) {
            for (MedicalTeam entity : entityList) {
                MedicalTeamVo vo = convertToTeamVo(entity);
                voList.add(vo);
            }
        }
        
        return voList;
    }

    /**
     * 获取团队详情
     */
    public MedicalTeamVo getTeam(Long id) {
        Assert.error(id == null, "团队ID不能为空");
        MedicalTeam team = medicalTeamService.getById(id);
        Assert.error(team == null, "未找到对应的团队");
        
        // 转换为VO对象
        return convertToTeamVo(team);
    }

//    /**
//     * 新增/编辑团队 - 信息
//     */
//    @Transactional(rollbackFor = Exception.class)
//    public void addOrEditTeam(TeamSubmitReq req) {
//        // 参数校验
//        Assert.error(req.getTeamName() == null, "团队名称不能为空");
//
//        MedicalTeam entity;
//        // 判断是新增还是编辑
//        if (req.getTeamId() != null) {
//            // 编辑
//            entity = medicalTeamService.getById(req.getTeamId());
//            Assert.error(entity == null, "未找到对应的团队");
//        } else {
//            // 新增
//            entity = new MedicalTeam();
//        }
//
//        // VO转Entity
//        entity.setTeamName(req.getTeamName());
//        entity.setTeamIntroduce(req.getTeamIntroduce());
//
//        medicalTeamService.saveOrUpdate(entity);
//
//        // 回填ID
//        if (req.getTeamId() == null && entity.getId() != null) {
//            req.setTeamId(entity.getId());
//        }
//
//        // 处理团队与用户关系 - 采用差异化更新方式
//        updateTeamUsers(req.getTeamId(), req.getTeamUserList());
//    }

    /**
     * 查询basic-user表
     */
    private void queryBasicUserTable() {

    }

    /**
     * 新增/编辑团队 - 信息
     */
    @Transactional(rollbackFor = Exception.class)
    public void addOrEditTeam(TeamSubmitReq req) {
        // 参数校验
        Assert.error(req.getTeamName() == null, "团队名称不能为空");

        MedicalTeam entity;
        // 判断是新增还是编辑
        if (req.getTeamId() != null) {
            // 编辑
            entity = medicalTeamService.getById(req.getTeamId());
            Assert.error(entity == null, "未找到对应的团队");
        } else {
            // 新增
            entity = new MedicalTeam();
        }

        // 处理团队照片资源ID
        Long teamPhotoResourceId = req.getTeamPhotoResourceId();
        String teamPhotoResourcePath = null;

        log.info("========执行到String teamPhotoResourcePath = null;=============teamPhotoResourceId: {}", teamPhotoResourceId);
        
        if (teamPhotoResourceId != null) {
            FileVo file = resourceClient.getFile(teamPhotoResourceId, RequestAttribute.TENANT_ID.getLong());
            Assert.error(file == null, "团队照片不存在");
            teamPhotoResourcePath = file.getPath();
        }

        log.info("========执行完teamPhotoResourcePath = null;=============teamPhotoResourcePath: {}", teamPhotoResourcePath);

        // VO转Entity
        entity.setTeamName(req.getTeamName());
        entity.setTeamIntroduce(req.getTeamIntroduce());
        entity.setTeamPhotoResourceId(teamPhotoResourceId);
        entity.setTeamPhotoResourcePath(teamPhotoResourcePath);

        log.info("=============================entity========================" + entity);
        log.info("===========================================================");
        log.info("==============================entity.tostring====================" + entity.toString());

        // 安全获取 RequestAttribute 值
        Long userId = null;
        Long tenantId = null;
        try {
            userId = RequestAttribute.USER_ID.getLong();
        } catch (Exception e) {
            log.warn("获取 USER_ID 失败: {}", e.getMessage());
        }

        try {
            tenantId = RequestAttribute.TENANT_ID.getLong();
        } catch (Exception e) {
            log.warn("获取 TENANT_ID 失败: {}", e.getMessage());
        }

        // 添加调试日志
        log.info("保存前实体信息: teamName={}, teamPhotoResourceId={}, userId={}, tenantId={}",
                entity.getTeamName(), entity.getTeamPhotoResourceId(), userId, tenantId);



        // 手动设置自动填充字段为 null，避免 "undefined" 问题
        // 手动设置自动填充字段，避免自动填充获取到 "undefined"
//        entity.setCreateUser(tenantId != null ? tenantId : 100100L); // 使用有效的租户ID作为用户ID
//        entity.setChanUser(tenantId != null ? tenantId : 100100L);
//        entity.setTenaId(tenantId != null ? tenantId : 100100L);
//        entity.setChanRequest(1L); // 设置一个默认值

        medicalTeamService.saveOrUpdate(entity);


        log.info("========执行完medicalTeamService.saveOrUpdate(entity)=============teamPhotoResourcePath: {}", teamPhotoResourcePath);



        // 确保团队ID有效
        Long finalTeamId = entity.getId();
        log.info("========执行完finalTeamId = entity.getId();=============finalTeamId: {}", finalTeamId);
        Assert.error(finalTeamId == null, "团队保存失败，无法获取团队ID");

//        // 回填ID
//        if (req.getTeamId() == null && entity.getId() != null) {
//            req.setTeamId(entity.getId());
//        }

        // 处理团队与用户关系 - 采用差异化更新方式
//        updateTeamUsers(req.getTeamId(), req.getTeamUserList());

        log.info("========开始执行resetTeamUsers(finalTeamId, req.getTeamUserList());=============");
        // 使用新的方法处理团队成员关系
        resetTeamUsers(finalTeamId, req.getTeamUserList());
    }

    /**
     * 上传团队照片
     * @param file 上传的文件
     * @param teamId 团队ID
     * @return 上传结果信息
     */
    @Transactional(rollbackFor = Exception.class)
    public String uploadTeamPhoto(MultipartFile file, Long teamId, Long fileId) {
        // 参数校验
        Assert.error(file == null || file.isEmpty(), "上传文件不能为空");
        Assert.error(teamId == null, "团队ID不能为空");
        
        // 检查团队是否存在
        MedicalTeam team = medicalTeamService.getById(teamId);
        Assert.error(team == null, "未找到对应的团队");

        Long teamPhotoResourceId = team.getTeamPhotoResourceId();
        // 如果fileId不为空，然后根据目前team的resourceid进行判断是否相同，若相同，则不进行上传
        if (fileId != null && teamPhotoResourceId != null && fileId.equals(teamPhotoResourceId)) {
            return "团队照片未发生更新";
        }

        // 获取原始文件名
        String originalFilename = file.getOriginalFilename();
        if (originalFilename == null || originalFilename.isEmpty()) {
            return "文件名不能为空";
        }
        
        try {
            // 调用资源服务上传文件
            FileVo fileVo = resourceClient.uploadFile(file, RequestAttribute.USER_ID.getLong(),
                    RequestAttribute.TENANT_ID.getLong());

            
            if (fileVo == null || fileVo.getFileId() == null) {
                return "文件上传失败";
            }
            
            // 更新团队照片信息
            team.setTeamPhotoResourceId(fileVo.getFileId());
            team.setTeamPhotoResourcePath(fileVo.getPath());
            
            // 保存更新
            medicalTeamService.updateById(team);
            
            return "上传成功";
        } catch (Exception e) {
            // 记录异常信息
            e.printStackTrace();
            return "上传失败: " + e.getMessage();
        }
    }

    /**
     * 删除团队
     */
    @Transactional(rollbackFor = Exception.class)
    public void deleteTeam(Long id) {
        Assert.error(id == null, "团队ID不能为空");
        MedicalTeam team = medicalTeamService.getById(id);
        Assert.error(team == null, "未找到对应的团队");
        
        // 1. 删除团队与用户的关联关系
        medicalRelationTeamUserService.removeByTeamId(id);
        
//        // 2. 删除资源文件
//        if (team.getTeamPhotoResourceId() != null) {
//            try {
//                // 调用资源服务删除文件
//                resourceClient.删除文件【我不知道是哪个方法】(team.getTeamPhotoResourceId());
//            } catch (Exception e) {
//                e.printStackTrace();
//            }
//        }
        
        // 3. 删除团队记录
        medicalTeamService.removeById(id);
    }

    /**
     * 获取医院历史列表
     */
    public List<MedicalHistoryVo> getHistories() {
        List<MedicalHistory> entityList = medicalHistoryService.list();
        
        // 转换为VO列表
        List<MedicalHistoryVo> voList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(entityList)) {
            for (MedicalHistory entity : entityList) {
                MedicalHistoryVo vo = convertToHistoryVo(entity);
                voList.add(vo);
            }
        }
        
        return voList;
    }

    /**
     * 获取医院历史详情
     */
    public MedicalHistoryVo getHistory(Long id) {
        Assert.error(id == null, "历史事件ID不能为空");
        MedicalHistory history = medicalHistoryService.getById(id);
        Assert.error(history == null, "未找到对应的历史事件");
        
        // 转换为VO对象
        return convertToHistoryVo(history);
    }

    /**
     * 新增/编辑医院历史
     */
    @Transactional(rollbackFor = Exception.class)
    public void addOrEditHistory(MedicalHistoryVo historyVo) {
        // 参数校验
        Assert.error(historyVo.getTitleName() == null, "事件标题不能为空");
        
        MedicalHistory entity;
        // 判断是新增还是编辑
        if (historyVo.getHistoryId() != null) {
            // 编辑
            entity = medicalHistoryService.getById(historyVo.getHistoryId());
            Assert.error(entity == null, "未找到对应的历史事件");

        } else {
            // 新增
            entity = new MedicalHistory();
        }
        
        // VO转Entity
//        entity.setStartTime(historyVo.getStartTime());
//        entity.setEndTime(historyVo.getEndTime());
        entity.setStartYear(historyVo.getStartYear());
        entity.setEndYear(historyVo.getEndYear());
        entity.setTitleName(historyVo.getTitleName());
        entity.setEventDesc(historyVo.getEventDesc());

        
        medicalHistoryService.saveOrUpdate(entity);
        
        // 回填ID
        if (historyVo.getHistoryId() == null && entity.getId() != null) {
            historyVo.setHistoryId(entity.getId());
        }
    }

    /**
     * 删除医院历史
     */
    @Transactional(rollbackFor = Exception.class)
    public void deleteHistory(Long id) {
        Assert.error(id == null, "历史事件ID不能为空");
        MedicalHistory history = medicalHistoryService.getById(id);
        Assert.error(history == null, "未找到对应的历史事件");
        
        medicalHistoryService.removeById(id);
    }

    /**
     * 获取医院历史列表（带排序）
     */
    public List<MedicalHistoryVo> getHistoriesSorted(String sortBy, String sortOrder) {
        // 创建查询条件
        LambdaQueryWrapper<MedicalHistory> wrapper = Wrappers.lambdaQuery();
        
        // 添加排序
        if (sortBy != null && !sortBy.isEmpty()) {
            boolean isAsc = "asc".equalsIgnoreCase(sortOrder);
            
            // 根据不同的排序字段进行排序
            switch (sortBy) {
//                case "startTime":
//                    wrapper.orderBy(true, isAsc, MedicalHistory::getStartTime);
//                    break;
//                case "endTime":
//                    wrapper.orderBy(true, isAsc, MedicalHistory::getEndTime);
//                    break;
                case "startYear":
                    wrapper.orderBy(true, isAsc, MedicalHistory::getStartYear);
                    break;
                case "endYear":
                    wrapper.orderBy(true, isAsc, MedicalHistory::getEndYear);
                    break;
                case "titleName":
                    wrapper.orderBy(true, isAsc, MedicalHistory::getTitleName);
                    break;
                default:
                    // 默认按创建时间排序
                    wrapper.orderBy(true, isAsc, MedicalHistory::getCreateTime);
            }
        } else {
            // 默认按创建时间升序排序
            wrapper.orderByAsc(MedicalHistory::getStartYear);
        }
        
        List<MedicalHistory> entityList = medicalHistoryService.list(wrapper);
        
        // 转换为VO列表
        List<MedicalHistoryVo> voList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(entityList)) {
            for (MedicalHistory entity : entityList) {
                MedicalHistoryVo vo = convertToHistoryVo(entity);
                voList.add(vo);
            }
        }
        
        return voList;
    }

//    /**
//     * 分页获取医院历史列表
//     */
//    public Map<String, Object> getHistoriesPage(Integer pageNum, Integer pageSize, String sortBy, String sortOrder) {
//        // 创建分页对象
//        Page<MedicalHistory> page = new Page<>(pageNum, pageSize);
//
//        // 创建查询条件
//        LambdaQueryWrapper<MedicalHistory> wrapper = Wrappers.lambdaQuery();
//    }

    /**
     * 批量新增医院历史
     */
    @Transactional(rollbackFor = Exception.class)
    public void batchAddHistories(List<MedicalHistoryVo> historyVos) {
        if (CollectionUtils.isEmpty(historyVos)) {
            return;
        }
        
        List<MedicalHistory> entities = new ArrayList<>();
        
        for (MedicalHistoryVo historyVo : historyVos) {
            // 参数校验
            Assert.error(historyVo.getTitleName() == null, "事件标题不能为空");
            
            MedicalHistory entity = new MedicalHistory();
            
            // VO转Entity
//            entity.setStartTime(historyVo.getStartTime());
//            entity.setEndTime(historyVo.getEndTime());
            entity.setStartYear(historyVo.getStartYear());
            entity.setEndYear(historyVo.getEndYear());
            entity.setTitleName(historyVo.getTitleName());
            entity.setEventDesc(historyVo.getEventDesc());
            
            entities.add(entity);
        }
        
        // 批量保存
        medicalHistoryService.saveBatch(entities);
        
        // 回填ID
        for (int i = 0; i < entities.size(); i++) {
            historyVos.get(i).setHistoryId(entities.get(i).getId());
        }
    }

    /**
     * 批量删除医院历史
     */
    @Transactional(rollbackFor = Exception.class)
    public void batchDeleteHistories(List<Long> historyIds) {
        if (CollectionUtils.isEmpty(historyIds)) {
            return;
        }
        
        // 批量删除
        medicalHistoryService.removeByIds(historyIds);
    }

    /**
     * 搜索团队 - 根据团队名称/成员搜索
     */
    public List<MedicalTeamVo> searchTeams(String keyword) {
        if (keyword == null || keyword.trim().isEmpty()) {
            // 如果关键词为空，返回所有团队
            return getTeamList();
        }
        
        // 1. 根据团队名称搜索
        LambdaQueryWrapper<MedicalTeam> teamWrapper = Wrappers.lambdaQuery();
        teamWrapper.like(MedicalTeam::getTeamName, keyword.trim());
        List<MedicalTeam> teamsByName = medicalTeamService.list(teamWrapper);
        
        // 2. 根据成员姓名搜索团队
        LambdaQueryWrapper<MedicalRelationTeamUser> userWrapper = Wrappers.lambdaQuery();
        userWrapper.like(MedicalRelationTeamUser::getUserName, keyword.trim());
        List<MedicalRelationTeamUser> userRelations = medicalRelationTeamUserService.list(userWrapper);
        
        // 获取通过成员搜索到的团队ID
        Set<Long> teamIdsByUser = userRelations.stream()
                .map(MedicalRelationTeamUser::getTeamId)
                .collect(Collectors.toSet());
        
        // 3. 合并结果
        Set<Long> allTeamIds = new HashSet<>();
        teamsByName.forEach(team -> allTeamIds.add(team.getId()));
        allTeamIds.addAll(teamIdsByUser);
        
        if (allTeamIds.isEmpty()) {
            return new ArrayList<>();
        }
        
        // 4. 根据团队ID获取完整团队信息
        List<MedicalTeam> allTeams = medicalTeamService.listByIds(allTeamIds);
        
        // 5. 转换为VO
        List<MedicalTeamVo> voList = new ArrayList<>();
        for (MedicalTeam team : allTeams) {
            MedicalTeamVo vo = convertToTeamVo(team);
            voList.add(vo);
        }
        
        return voList;
    }

    /**
     * 获取研究动向列表
     */
    public List<MedicalResearchTrendVo> getResearchList() {
        List<MedicalResearchTrend> entityList = medicalResearchTrendService.list();
        
        List<MedicalResearchTrendVo> voList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(entityList)) {
            for (MedicalResearchTrend entity : entityList) {
                MedicalResearchTrendVo vo = convertToResearchVo(entity);
                voList.add(vo);
            }
        }
        
        return voList;
    }

    /**
     * 新增/编辑研究动向
     */
    @Transactional(rollbackFor = Exception.class)
    public void addOrEditResearch(MedicalResearchTrendVo req) {
        // 参数校验
        Assert.error(req.getTrendTitle() == null, "动态标题不能为空");
        Assert.error(req.getAuthorUnit() == null, "作者单位不能为空");

        MedicalResearchTrend entity;
        // 判断是新增还是编辑
        if (req.getTrendId() != null) {
            // 编辑
            entity = medicalResearchTrendService.getById(req.getTrendId());
            Assert.error(entity == null, "未找到对应的研究动向");
        } else {
            // 新增
            entity = new MedicalResearchTrend();
            entity.setReleaseTime(UtilTime.getCurrentFormattedTimeMinute());
        }

        // 处理封面照片资源ID
        Long trendPhotoResourceId = req.getTrendPhotoResourceId();
        String trendPhotoResourcePath = null;

        log.info("========执行到coverPhotoResourcePath处理=============coverPhotoResourceId: {}", trendPhotoResourceId);
        
        if (trendPhotoResourceId != null) {
            FileVo file = resourceClient.getFile(trendPhotoResourceId, RequestAttribute.TENANT_ID.getLong());
            Assert.error(file == null, "封面照片不存在");
            trendPhotoResourcePath = file.getPath();
        }

        log.info("========执行完coverPhotoResourcePath处理=============coverPhotoResourcePath: {}", trendPhotoResourcePath);

        // VO转Entity
        entity.setTrendTitle(req.getTrendTitle());
        entity.setAuthorUnit(req.getAuthorUnit());
        entity.setContent(req.getContent());
        entity.setTrendPhotoResourceId(trendPhotoResourceId);
        entity.setTrendPhotoResourcePath(trendPhotoResourcePath);


        log.info("=============================research entity========================" + entity);

        medicalResearchTrendService.saveOrUpdate(entity);

        log.info("========执行完medicalResearchService.saveOrUpdate(entity)=============");
    }

    /**
     * 获取研究动向详情
     */
    public MedicalResearchTrendVo getResearch(Long id) {
        Assert.error(id == null, "研究动向ID不能为空");
        MedicalResearchTrend research = medicalResearchTrendService.getById(id);
        Assert.error(research == null, "未找到对应的研究动向");

        // 转换为VO对象
        return convertToResearchVo(research);
    }

    /**
     * 删除研究动向
     */
    @Transactional(rollbackFor = Exception.class)
    public void deleteResearch(Long id) {
        Assert.error(id == null, "研究动向ID不能为空");
        MedicalResearchTrend research = medicalResearchTrendService.getById(id);
        Assert.error(research == null, "未找到对应的研究动向");

        medicalResearchTrendService.removeById(id);
    }

    /**
     * 搜索研究动向 - 根据标题或作者单位搜索
     */
    public List<MedicalResearchTrendVo> searchResearch(String keyword) {
        if (keyword == null || keyword.trim().isEmpty()) {
            // 如果关键词为空，返回所有研究动向
            return getResearchList();
        }

        // 根据标题或作者单位搜索
        LambdaQueryWrapper<MedicalResearchTrend> wrapper = Wrappers.lambdaQuery();
        wrapper.like(MedicalResearchTrend::getTrendTitle, keyword.trim())
               .or()
               .like(MedicalResearchTrend::getAuthorUnit, keyword.trim());

        List<MedicalResearchTrend> entityList = medicalResearchTrendService.list(wrapper);

        List<MedicalResearchTrendVo> voList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(entityList)) {
            for (MedicalResearchTrend entity : entityList) {
                MedicalResearchTrendVo vo = convertToResearchVo(entity);
                voList.add(vo);
            }
        }

        return voList;
    }

    /**
     * 搜索研究动向 - 根据多条件搜索（关键词 + 日期范围）
     */
    public List<MedicalResearchTrendVo> searchResearch(ResearchSearchReq req) {
        LambdaQueryWrapper<MedicalResearchTrend> wrapper = Wrappers.lambdaQuery();

        // 1. 关键词搜索（标题或作者单位）
        if (req.getKeyword() != null && !req.getKeyword().trim().isEmpty()) {
            String keyword = req.getKeyword().trim();
            wrapper.and(w -> w.like(MedicalResearchTrend::getTrendTitle, keyword)
                           .or()
                           .like(MedicalResearchTrend::getAuthorUnit, keyword));
        }

        log.info("========执行到searchResearch=============wrapper: {}", wrapper);
        // 2. 发布日期范围筛选
        if (req.getStartDate() != null && !req.getStartDate().trim().isEmpty()) {
            log.info("========执行到searchResearch=============startDate: {}", req.getStartDate());
            wrapper.ge(MedicalResearchTrend::getReleaseTime, req.getStartDate().trim());
        }

        if (req.getEndDate() != null && !req.getEndDate().trim().isEmpty()) {
            wrapper.le(MedicalResearchTrend::getReleaseTime, req.getEndDate().trim());
        }

        // 按发布时间倒序排列
        wrapper.orderByDesc(MedicalResearchTrend::getReleaseTime);

        List<MedicalResearchTrend> entityList = medicalResearchTrendService.list(wrapper);

        List<MedicalResearchTrendVo> voList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(entityList)) {
            for (MedicalResearchTrend entity : entityList) {
                MedicalResearchTrendVo vo = convertToResearchVo(entity);
                voList.add(vo);
            }
        }

        return voList;
    }

    /**
     * 转换研究动向实体为VO
     */
    private MedicalResearchTrendVo convertToResearchVo(MedicalResearchTrend entity) {
        MedicalResearchTrendVo vo = new MedicalResearchTrendVo();
        vo.setTrendId(entity.getId());
        vo.setTrendTitle(entity.getTrendTitle());
        vo.setAuthorUnit(entity.getAuthorUnit());
        vo.setTrendPhotoResourceId(entity.getTrendPhotoResourceId());
        vo.setTrendPhotoResourcePath(entity.getTrendPhotoResourcePath());
        vo.setContent(entity.getContent());
        vo.setReleaseTime(entity.getReleaseTime());
        return vo;
    }

    // ----------------------------------  转化工具方法 ----------------------------------


    /**
     * 将MedicalTeam实体转换为MedicalTeamVo
     */
    private MedicalTeamVo convertToTeamVo(MedicalTeam entity) {
        if (entity == null) {
            return null;
        }

        System.out.println("========userid======"+RequestAttribute.USER_ID.getLong());
        System.out.println("========teneid======"+RequestAttribute.TENANT_ID.getLong());

        MedicalTeamVo vo = new MedicalTeamVo();
        vo.setTeamId(entity.getId());
        vo.setTeamName(entity.getTeamName());
        vo.setTeamPhotoResourceId(entity.getTeamPhotoResourceId());
        vo.setTeamPhotoResourcePath(entity.getTeamPhotoResourcePath());
        vo.setTeamIntroduce(entity.getTeamIntroduce());

        // 对应团队的团队成员
        LambdaQueryWrapper<MedicalRelationTeamUser> teamUserQuery = Wrappers.lambdaQuery();
        teamUserQuery.eq(MedicalRelationTeamUser::getTeamId, entity.getId());
        List<MedicalRelationTeamUser> teamUserList = medicalRelationTeamUserService.list(teamUserQuery);
        vo.setTeamUserList(teamUserList.stream().map(teamUser -> {
            TeamUserReq teamUserReq = new TeamUserReq();
            teamUserReq.setUserId(teamUser.getUserId());
            teamUserReq.setUserName(teamUser.getUserName());
            teamUserReq.setDepaId(teamUser.getDepaId());
            teamUserReq.setDepaName(teamUser.getDepaName());
            teamUserReq.setTitle(teamUser.getTitle());

            UserInfo userInfo = userClient.queryUserInfo(new UserInfoQuery(
                    RequestAttribute.USER_ID.getLong(),
                    RequestAttribute.TENANT_ID.getLong()));
            String description = userInfo.getDescription();
            System.out.println("====到headimgid====");
            Long headImgId = userInfo.getHeadImgId();
            System.out.println("====完成headimgid=======");
//            String headImgUrl = resourceClient.getFile(headImgId, RequestAttribute.TENANT_ID.getLong()).getPath();
            String headImgUrl = userInfo.getHeadImgUrl();

            teamUserReq.setDesc(description);
            teamUserReq.setHeadImgId(headImgId);
            teamUserReq.setHeadImgUrl(headImgUrl);

            return teamUserReq;
        }).collect(Collectors.toList()));

        return vo;
    }
    
    /**
     * 将MedicalHistory实体转换为MedicalHistoryVo
     */
    private MedicalHistoryVo convertToHistoryVo(MedicalHistory entity) {
        if (entity == null) {
            return null;
        }
        
        MedicalHistoryVo vo = new MedicalHistoryVo();
        vo.setHistoryId(entity.getId());
//        vo.setStartTime(entity.getStartTime());
//        vo.setEndTime(entity.getEndTime());
        vo.setStartYear(entity.getStartYear());
        vo.setEndYear(entity.getEndYear());
        vo.setTitleName(entity.getTitleName());
        vo.setEventDesc(entity.getEventDesc());
        
        return vo;
    }



    // ----------------------------------------------------------- 封装方法----------------------------------------------------------

    /**
     * 差异化更新团队成员
     * - 新增: 不需要有userId
     * - 编辑: 需要有userId
     * - 删除: userList中不包含该userId
     * @param teamId 团队ID
     * @param newUserList 新的团队成员列表
     */
    private void updateTeamUsers(Long teamId, List<TeamUserReq> newUserList) {
        if (teamId == null) {
            return;
        }

        // 如果新列表为空，则删除所有关联
        if (newUserList == null || newUserList.isEmpty()) {
            medicalRelationTeamUserService.removeByTeamId(teamId);
            return;
        }

        // 1. 获取当前团队的所有成员关联
        List<MedicalRelationTeamUser> existingRelations = medicalRelationTeamUserService.listByTeamId(teamId);

        // 2. 将现有成员按userId分组，便于查找
        Map<Long, MedicalRelationTeamUser> existingMap = new HashMap<>();
        if (existingRelations != null) {
            for (MedicalRelationTeamUser relation : existingRelations) {
                if (relation.getUserId() != null) {
                    existingMap.put(relation.getUserId(), relation);
                }
            }
        }

        // 3. 收集需要保留、新增和删除的记录
        List<MedicalRelationTeamUser> toAdd = new ArrayList<>();
        Set<Long> processedUserIds = new HashSet<>();

        // 处理新列表中的每个成员
        for (TeamUserReq newUser : newUserList) {
            if (newUser.getUserId() == null) {
                // 没有ID的记录视为新增
                MedicalRelationTeamUser newRelation = new MedicalRelationTeamUser();
                newRelation.setTeamId(teamId);
                newRelation.setUserId(newUser.getUserId());
                newRelation.setUserName(newUser.getUserName());
                newRelation.setDepaId(newUser.getDepaId());
                newRelation.setDepaName(newUser.getDepaName());
                newRelation.setTitle(newUser.getTitle());
                toAdd.add(newRelation);
                continue;
            }

            processedUserIds.add(newUser.getUserId());

            // 检查是否存在此用户关联
            MedicalRelationTeamUser existingRelation = existingMap.get(newUser.getUserId());
            if (existingRelation == null) {
                // 不存在，需要新增
                MedicalRelationTeamUser newRelation = new MedicalRelationTeamUser();
                newRelation.setTeamId(teamId);
                newRelation.setUserId(newUser.getUserId());
                newRelation.setUserName(newUser.getUserName());
                newRelation.setDepaId(newUser.getDepaId());
                newRelation.setDepaName(newUser.getDepaName());
                newRelation.setTitle(newUser.getTitle());
                toAdd.add(newRelation);
            } else {
                // 存在，检查是否需要更新
                boolean needUpdate = false;

                if (!Objects.equals(existingRelation.getUserName(), newUser.getUserName())) {
                    existingRelation.setUserName(newUser.getUserName());
                    needUpdate = true;
                }

                if (!Objects.equals(existingRelation.getDepaId(), newUser.getDepaId())) {
                    existingRelation.setDepaId(newUser.getDepaId());
                    needUpdate = true;
                }

                if (!Objects.equals(existingRelation.getDepaName(), newUser.getDepaName())) {
                    existingRelation.setDepaName(newUser.getDepaName());
                    needUpdate = true;
                }

                if (!Objects.equals(existingRelation.getTitle(), newUser.getTitle())) {
                    existingRelation.setTitle(newUser.getTitle());
                    needUpdate = true;
                }

                // 如果有更新，保存更改
                if (needUpdate) {
                    medicalRelationTeamUserService.updateById(existingRelation);
                }
            }
        }

        // 4. 找出需要删除的记录
        List<Long> toDeleteIds = new ArrayList<>();
        for (MedicalRelationTeamUser relation : existingRelations) {
            if (relation.getUserId() != null && !processedUserIds.contains(relation.getUserId())) {
                toDeleteIds.add(relation.getId());
            }
        }

        // 5. 执行批量操作
        if (!toAdd.isEmpty()) {
            medicalRelationTeamUserService.saveBatch(toAdd);
        }

        if (!toDeleteIds.isEmpty()) {
            medicalRelationTeamUserService.removeByIds(toDeleteIds);
        }
    }

    /**
     * 重新设置团队成员 - 先删除所有，再重新插入
     * @param teamId 团队ID
     * @param newUserList 新的团队成员列表
     */
    private void resetTeamUsers(Long teamId, List<TeamUserReq> newUserList) {

        log.info("======================11111111111111111111111111111111111111111111111111111111111====================");

        if (teamId == null) {
            return;
        }

        log.info("======================22222222222222222222222222222222222222222222222222222222222====================");

        // 1. 先删除该团队的所有成员关联
        medicalRelationTeamUserService.removeByTeamId(teamId);

        log.info("======================33333333333333333333333333333333333333333333333333333333333====================");

        // 2. 如果新列表为空，则直接返回
        if (newUserList == null || newUserList.isEmpty()) {
            return;
        }

        log.info("======================44444444444444444444444444444444444444444444444444444444444====================");

        // 安全获取 RequestAttribute 值
        Long userId = null;
        Long tenantId = null;
        try {
            userId = RequestAttribute.USER_ID.getLong();
        } catch (Exception e) {
            log.warn("获取 USER_ID 失败: {}", e.getMessage());
        }

        try {
            tenantId = RequestAttribute.TENANT_ID.getLong();
        } catch (Exception e) {
            log.warn("获取 TENANT_ID 失败: {}", e.getMessage());
        }

        // 3. 重新插入所有成员
        List<MedicalRelationTeamUser> toAdd = new ArrayList<>();
        for (TeamUserReq newUser : newUserList) {
            MedicalRelationTeamUser newRelation = new MedicalRelationTeamUser();
            newRelation.setTeamId(teamId);
            newRelation.setUserId(newUser.getUserId());
            newRelation.setUserName(newUser.getUserName());
            newRelation.setDepaId(newUser.getDepaId());
            newRelation.setDepaName(newUser.getDepaName());
            newRelation.setTitle(newUser.getTitle());
//            newRelation.setCreateUser(tenantId != null ? tenantId : 100100L); // 使用有效的租户ID作为用户ID
//            newRelation.setChanUser(tenantId != null ? tenantId : 100100L);
//            newRelation.setTenaId(tenantId != null ? tenantId : 100100L);
//            newRelation.setChanRequest(1L); // 设置一个默认值
            toAdd.add(newRelation);
        }

        log.info("======================55555555555555555555555555555555555555555555555555555555555====================");

        // 4. 批量插入
        if (!toAdd.isEmpty()) {
            medicalRelationTeamUserService.saveBatch(toAdd);
        }

        log.info("======================66666666666666666666666666666666666666666666666666666666666====================");
    }
}
