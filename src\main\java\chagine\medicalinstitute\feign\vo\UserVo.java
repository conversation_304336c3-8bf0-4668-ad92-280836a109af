package chagine.medicalinstitute.feign.vo;


import chagine.medicalinstitute.serialize.LongJsonSerializer;
import chagine.medicalinstitute.serialize.StringJsonDeserializer;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.Data;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class UserVo {

    @JsonProperty(value = "user_id")
    @JsonDeserialize(using = chagine.medicalinstitute.serialize.StringJsonDeserializer.class)
    @JsonSerialize(using = chagine.medicalinstitute.serialize.LongJsonSerializer.class)
    private Long userId;

    @JsonProperty(value = "user_name")
    private String userName;

    private String py;

    /**
     * 部门ID
     */
    @JsonProperty(value = "depa_id")
    @JsonDeserialize(using = chagine.medicalinstitute.serialize.StringJsonDeserializer.class)
    @JsonSerialize(using = chagine.medicalinstitute.serialize.LongJsonSerializer.class)
    private Long depaId;
    @JsonProperty(value = "depa_name")
    private String depaName;

    @JsonProperty(value = "depa_code")
    private String depaCode;

    /**
     * 职称
     */
    @JsonProperty(value = "title")
    private String title;

    /**
     * 医院
     */
    @JsonProperty(value = "hospital")
    private String hospital;

    @JsonProperty(value = "gender")
    private Integer gender;

    @JsonProperty(value = "job_number")
    private String jobNumber;

    @JsonProperty(value = "phone")
    private String phone;

    @JsonProperty(value = "s_phone")
    private String sPhone;

//    @JsonProperty(value = "hidden_phone")
//    public String getHiddenPhone() {
//        return HiddenUtil.hiddenPhone(phone);
//    }


    @JsonProperty(value = "password")
    private String password;

    @JsonProperty(value = "pwd_seed")
    private String pwdSeed;

    @JsonProperty(value = "email")
    private String email;

    @JsonProperty(value = "is_admin")
    private Integer isAdmin;

    @JsonProperty(value = "head_img_id")
    @JsonDeserialize(using = StringJsonDeserializer.class)
    @JsonSerialize(using = LongJsonSerializer.class)
    private Long headImgId;

    @JsonProperty(value = "head_img_url")
    private String headImgUrl;

    @JsonProperty(value = "description")
    private String description;

    @JsonProperty(value = "good_at")
    private String goodAt;

    @JsonProperty(value = "is_doctor")
    private Integer isDoctor;

    @JsonProperty(value = "qualification_img_id")
    @JsonDeserialize(using = StringJsonDeserializer.class)
    @JsonSerialize(using = LongJsonSerializer.class)
    private Long qualificationImgId;

    @JsonProperty(value = "qualification_img_url")
    private String qualificationImgUrl;

    @JsonProperty(value = "credentials_img_id")
    @JsonDeserialize(using = StringJsonDeserializer.class)
    @JsonSerialize(using = LongJsonSerializer.class)
    private Long credentialsImgId;

    @JsonProperty(value = "credentials_img_url")
    private String credentialsImgUrl;

    /*
     * 服务人数
     */
    @JsonProperty(value = "serv_num")
    private Integer servNum;
    /*
     * 星级
     */
    private Integer star;


    @JsonProperty(value = "address")
    private String address;

    @JsonProperty(value = "create_user")
    private Long createUser;

    @JsonProperty(value = "create_time")
    private Long createTime;


    //是否有环信账号   0:否 1是
    @JsonProperty(value = "is_easem_user")
    private Integer isEasemUser;

    @JsonProperty(value = "easem_user")
    private String easemUser;


//    //咨询费
//    @JsonProperty(value = "consult_fee")
//    @JsonSerialize(using = MoneySerializer.class)
//    @JsonDeserialize(using = MoneyDeserializer.class)
//    private Integer consultFee;


    //是否接受咨询,默认：0，0-不接诊;1-接诊
    @JsonProperty(value = "is_consult")
    private Integer isConsult;


//    //处方费
//    @JsonProperty(value = "pres_fee")
//    @JsonSerialize(using = MoneySerializer.class)
//    @JsonDeserialize(using = MoneyDeserializer.class)
//    private Integer presFee;

    //是否接受处方,默认：0，0-否;1-是
    @JsonProperty(value = "is_pres")
    private Integer isPres;


    //门特处方费
//    @JsonProperty(value = "mentor_fee")
//    @JsonSerialize(using = MoneySerializer.class)
//    @JsonDeserialize(using = MoneyDeserializer.class)
//    private Integer mentorFee;

    //是否接受门特处方,默认：0，0-否;1-是
    @JsonProperty(value = "is_mentor")
    private Integer isMentor;


    @JsonProperty(value = "sign_img_id")
    @JsonDeserialize(using = StringJsonDeserializer.class)
    @JsonSerialize(using = LongJsonSerializer.class)
    private Long signImgId;

    @JsonProperty(value = "sign_img_url")
    private String signImgUrl;

//    /**
//     * 员工角色列表
//     */
//    @JsonProperty(value = "role_list")
//    private List<RoleVo> roleList;

    //医院名称
    @JsonProperty(value = "hospital_name")
    private String hospitalName;


    //有效状态 0-停用;1-启用
    @JsonProperty(value = "enable")
    private Integer enable;

    //是否有腾讯IM账号   0:否 1是
    @JsonProperty(value = "is_tim_user")
    private Integer isTimUser;

    @JsonProperty(value = "tim_identifier")
    private String timIdentifier;


    @JsonProperty(value = "tim_user_sig")
    private String timUserSig;

    @JsonProperty(value = "seal_data")
    private String sealData;

    @JsonProperty(value = "district_id")
    private Long districtId;

    @JsonProperty(value = "district_name")
    private String districtName;

    /**
     * his中的医生ID
     */
    @JsonProperty(value = "his_doctor_id")
    private String hisDoctorId;

    @JsonProperty(value = "his_sign_pic")
    private String hisSignPic;

    @JsonProperty(value = "first_login")
    private Integer firstLogin;

    /**
     * 是否选择了该用户
     */
    @JsonProperty("choose_user")
    private Boolean chooseUser;
}
