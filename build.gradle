//插件
plugins {
    id 'webApplicationPlugin' version "V1.0.1-SNAPSHOT"
}

//依赖
dependencies {
    api(annotationProcessor("chagine:chagine-service-api:V1.0.1-SNAPSHOT"))
    api(annotationProcessor("chagine:chagine-service-extension:V1.0.1-SNAPSHOT"))
//    implementation 'com.oracle.database.jdbc:ojdbc8'
//    implementation 'com.baomidou:dynamic-datasource-spring-boot-starter'
//    runtimeOnly 'com.oracle.database.nls:orai18n:21.6.0.0.1'
    implementation 'org.apache.poi:poi:5.2.3'
    implementation 'org.apache.poi:poi-ooxml:5.2.3'
}

project.configurations.configureEach {
    resolutionStrategy.cacheChangingModulesFor 0, 'seconds'
}

