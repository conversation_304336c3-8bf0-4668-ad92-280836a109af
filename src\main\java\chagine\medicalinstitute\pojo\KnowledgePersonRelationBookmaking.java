package chagine.medicalinstitute.pojo;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

@TableName(value ="knowledge_person_relation_bookmaking")
@Data
public class KnowledgePersonRelationBookmaking {

    /**
     * ID
     */
    private Long id;

    /**
     * 人物id
     */
    private Long personId;

    /**
     * 著作id
     */
    private Long bookmakingId;

    /**
     * 人物姓名
     */
    private String personName;

    /**
     * 著作名称
     */
    private String bookmakingName;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createUser;

    /**
     * 变更时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime chanTime;

    /**
     * 变更人
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long chanUser;

    /**
     * 变更引起的请求
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long chanRequest;

    /**
     * 租户id(合作机构租户号)
     */
    @TableField(fill = FieldFill.INSERT)
    private Long tenaId;
}
